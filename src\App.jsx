// App.jsx

import React from "react";
import ReactDOM from "react-dom/client";
import "./About.css";

const App = () => {
  return (
    <div className="parent-div ">
      <div className="about-us-5581109 pos-abs">
        {/* NAV BAR1 */}
        <section className="nav-bar-5991098 pos-abs">
          <div className="kuberaa-logo-2-709926 pos-abs">
            <img
              src="https://s3-alpha-sig.figma.com/img/947c/518a/a96e128dd3a5444381161e74e580d564?Expires=1748822400&Key-Pair-Id=APKAQ4GOSFWCW27IBOMQ&Signature=G7pL8R12ijoiHJPKjGj1Ybj18u~OV27YXncaNcp9G5dXnqfKhJPgVC-bCaSSCrX6RAZ62cX8HNKs9h~jod8MD~1hd3WXCo3zYBvujzwFr5zxxQtE6cWJTILbw-mPsQzZcY2brXZ~JEMp6~vJEKIEw98AqnWGdKBkqVZKaZ3kiN9aUwL1io83vEElk84tVBBNAesjTAKI~q1ZeNB52kabLxCVy2pJ7OzJSPm-Q2XWdeAS8AVSdukdUzXgiX1K-scwggFUIPy0vfonyO6~1w5S9XRpw-ga7das0dbnrDAqTwp0b4XUFvbZA6eF97lG6BqySR7y7Wg~q6kf5NJu1~NTOQ__"
              className="pos-abs image-div bg-no-repeat fill-parent bg-cover nodeBg-709926 "
              alt="709926-ALT"
            />{" "}
          </div>

          <div className="group-1-5991099 pos-abs">
            <div className="home-5991100 pos-abs">
              <span className="home-5991100-0">{`Home`}</span>
            </div>

            <div className="projects-5991101 pos-abs">
              <span className="projects-5991101-0">{`Projects`}</span>
            </div>

            <div className="about-us-5991102 pos-abs">
              <span className="about-us-5991102-0">{`About us`}</span>
            </div>

            <div className="contact-us-5991103 pos-abs">
              <span className="contact-us-5991103-0">{`Contact us`}</span>
            </div>
          </div>
        </section>

        <div className="image-23-5581110 pos-abs">
          <img
            src="https://s3-alpha-sig.figma.com/img/d276/74b9/9f6f1470c95c721f6a45d53df17ee273?Expires=1748822400&Key-Pair-Id=APKAQ4GOSFWCW27IBOMQ&Signature=nhOHRUVuXu7bCK3T2BStBIt1pvMsmVDyJ1idzFYwm-C9PYVQqmoL1cS-iHnHqI6Jtw~TC-pNasl~ne514R0B-OWKuAlARdcYUDGrBSsXCiJ14fEjC-C7zHiQ9j8JVHaHfFvm4e6wrbMOT4vQWRosEXcR2Q9iVX5dosGYBTDYi9e2vPzdTsqn9GO8iuK3JFrjsnZRPvWKUrunX6lGhPt78TfWgaQ3R9sRdrSs1goxlEB3q6qWZWiaJIoOX77rlvw7HyLFBEzBhJYbzEnpyK3Mpm4YrrXbkHAVDtNBzSseBofIQj~FQY9HpVteTq7aXaUu-0Lsl-o0FEVKQa7ayd4DKw__"
            className="pos-abs image-div bg-no-repeat bg-crop nodeBg-5581110 "
            alt="5581110-ALT"
          />{" "}
        </div>

        <div className="image-15-5581116 pos-abs">
          <img
            src="https://s3-alpha-sig.figma.com/img/c6b2/dac1/83381d611a7ef2d08b62e58e2e26c916?Expires=1748822400&Key-Pair-Id=APKAQ4GOSFWCW27IBOMQ&Signature=uSAOVnEb8fD3YaB5IUsI2AzJzrOWQyloBsWVSL7PghbK6nrIq4JJWInKpjg9Eu0ipO5o-EZuBU0pzktROnXIfiwHu0yiezkj6-zFkpLHvzJSLDNeNZ28d3uWBF89a7LvLR7Ppnjf5gMb3d2qAkwT6GsVc1f-cAfTDt5~iV1gjk1LmVm~o9ZxtX8l6zbxLzovTb5CCtvgZbZWBdUgNG1WOyrSZbiG~WWSbxH6Yp4UWgtFlcjwf0XjG3S6qcDo9RVMEph3wvAkGRJKlrVQpDOCL8ryk5lPRWxYpNBj4sTFzyv9ujeV3qukOTBgTF0S8rYnn0VfIjR7yqTJ4BYe~-lgaA__"
            className="pos-abs image-div bg-no-repeat fill-parent bg-cover nodeBg-5581116 "
            alt="5581116-ALT"
          />{" "}
        </div>
        {/* Group 21 */}
        <section className="group-2-5581111 pos-abs">
          <div className="home-5581112 pos-abs">
            <span className="home-5581112-0">{`Home`}</span>
          </div>

          <div className="projects-5581113 pos-abs">
            <span className="projects-5581113-0">{`Projects`}</span>
          </div>

          <div className="about-us-5581114 pos-abs">
            <span className="about-us-5581114-0">{`About us`}</span>
          </div>

          <div className="contact-us-5581115 pos-abs">
            <span className="contact-us-5581115-0">{`Contact us`}</span>
          </div>
        </section>

        <div className="about-us-5581117 pos-abs">
          <span className="about-us-5581117-0">{`ABOUT  US`}</span>
        </div>

        <div className="behind-the-succ-5581118 pos-abs">
          <span className="behind-the-succ-5581118-0">{`BEHIND THE SUCCESS`}</span>
        </div>

        <div className="absolutely-here-5581119 pos-abs">
          <span className="absolutely-here-5581119-0">{`Absolutely! Here's a shorter and more concise version of the About Us page for a real estate business:
About Us
At [Your Company Name], we make real estate simple, personal, and rewarding. Whether you're buying, selling, or investing, our experienced team is here to guide you every step of the way.
We specialize in residential and commercial properties, offering expert advice, local market knowledge, and a commitment to client satisfaction.
With a passion for property and a focus on results, we’re not just helping you find a space—we’re helping you find your future.
`}</span>
        </div>

        <div className="k-kemparaj-5581121 pos-abs">
          <span className="k-kemparaj-5581121-0">{`K. Kemparaj`}</span>
        </div>

        <div className="ceo-of-the-comp-5581122 pos-abs">
          <span className="ceo-of-the-comp-5581122-0">{`CEO OF THE COMPANY`}</span>
        </div>

        <div className="image-24-5581120 pos-abs">
          <img
            src="https://s3-alpha-sig.figma.com/img/1691/2d28/88121cd5f99107210bdde8c486177a78?Expires=1748822400&Key-Pair-Id=APKAQ4GOSFWCW27IBOMQ&Signature=Ni3vUxBUubA1WzgTwdP6Tl-hsR2HznNQqsXmJwOxF0khrdDMFJogkWyogNXsak-bmQHs2LIzxxHeuDZQXDBgTIfTlGu7q2bExiBMLkwO6VMwIbQPm9bXinxS9wEhMBZ2QhJahCOdOOi5vFaVS07owQUrPCTrHQUVPOfQ~c-jEU9sodMiw3xqflxFByDxJ31DK7yF7NUo8GmsqZ-eB~dxwBUKithaXLxlx7x0ckhQWOmdsnFA4iZ5Ic5TYi-cLNSW8VSxF~68451QmfMkDVJEAngHbnYGBGqcoAt5quPUIGIjdkeMkSaxa0WyLH9tigLmwyn6hoWGb~Szq9uGpQ8EKw__"
            className="pos-abs image-div bg-no-repeat fill-parent bg-cover nodeBg-5581120 "
            alt="5581120-ALT"
          />{" "}
        </div>

        <div className="image-2-599934 pos-abs">
          <img
            src="https://firebasestorage.googleapis.com/v0/b/figma-plugin-a7287.appspot.com/o/user-images%2F24-may-2025%2Fpritam1748067673257%2Fimage-599-934.png?alt=media&token=0"
            className="pos-abs pos-init fill-parent bg-contain bg-no-repeat image-div  object-fit"
            alt="599934-ALT"
          />{" "}
        </div>
        {/* Frame 171 */}
        <section className="frame-17-5581134 pos-abs">
          <div className="our-mission-5581135 pos-abs">
            <span className="our-mission-5581135-0">{`OUR MISSION`}</span>
          </div>

          <div className="image-20-5581144 pos-abs">
            <img
              src="https://s3-alpha-sig.figma.com/img/c6b2/dac1/83381d611a7ef2d08b62e58e2e26c916?Expires=1748822400&Key-Pair-Id=APKAQ4GOSFWCW27IBOMQ&Signature=uSAOVnEb8fD3YaB5IUsI2AzJzrOWQyloBsWVSL7PghbK6nrIq4JJWInKpjg9Eu0ipO5o-EZuBU0pzktROnXIfiwHu0yiezkj6-zFkpLHvzJSLDNeNZ28d3uWBF89a7LvLR7Ppnjf5gMb3d2qAkwT6GsVc1f-cAfTDt5~iV1gjk1LmVm~o9ZxtX8l6zbxLzovTb5CCtvgZbZWBdUgNG1WOyrSZbiG~WWSbxH6Yp4UWgtFlcjwf0XjG3S6qcDo9RVMEph3wvAkGRJKlrVQpDOCL8ryk5lPRWxYpNBj4sTFzyv9ujeV3qukOTBgTF0S8rYnn0VfIjR7yqTJ4BYe~-lgaA__"
              className="pos-abs image-div bg-no-repeat bg-crop nodeBg-5581144 "
              alt="5581144-ALT"
            />{" "}
          </div>

          <div className="frame-12-5581136 pos-abs">
            <div className="to-deliver-arch-5581137 pos-abs">
              <span className="to-deliver-arch-5581137-0">{`To deliver architecturally distinctive villas that combine elegance with thoughtful functionality.`}</span>
            </div>
          </div>

          <div className="frame-13-5581138 pos-abs">
            <div className="to-foster-a-vib-5581139 pos-abs">
              <span className="to-foster-a-vib-5581139-0">{`To foster a vibrant, secure, and green community in the heart of Bangalore.`}</span>
            </div>
          </div>

          <div className="frame-14-5581140 pos-abs">
            <div className="to-prioritize-s-5581141 pos-abs">
              <span className="to-prioritize-s-5581141-0">{`To prioritize sustainable practices, premium quality, and long-lasting value in every villa we build.`}</span>
            </div>
          </div>

          <div className="frame-15-5581142 pos-abs">
            <div className="to-continuously-5581143 pos-abs">
              <span className="to-continuously-5581143-0">{`To continuously innovate in design and amenities, keeping homeowners at the heart of every decision.`}</span>
            </div>
          </div>
        </section>
        {/* Frame 181 */}
        <section className="frame-18-5581145 pos-abs">
          <img
            src="https://s3-alpha-sig.figma.com/img/484a/e880/145eee6b9ee948e607112f13e92fd35b?Expires=1748822400&Key-Pair-Id=APKAQ4GOSFWCW27IBOMQ&Signature=sFC9aiOVoIMztq4ZEnHcbzD5s2hwSoIz-k8TMypzWE5XUaS9pe8pit-z5JfDv-8atVwkQi2EK~yJLuQP73KQOWoV0qUQhfp6uVUL6j58mLOzXX9VQIGpLaB0QbUwwvhZq92deSKcjeOYU~3R0DSdortP834ib1fxPUQU6Ki5~9hmIS-2g-AI-lNWTDtNCvR7tsv35jzTpVJsTIT-N8OPFq5JIZpCu58CeIPKtPjZULTHLvlfMVgd7dtoWlE-kdwjvkA56i6hFPqu6KzU22m2SqeyXjrdSnBWC6khSsCuYHkwK8L~fhQWW1kdEH11S~bmZ-bjoJidkFkyRwNH~WwBHA__"
            className="pos-abs image-div bg-no-repeat fill-parent bg-cover nodeBg-5581145 "
            alt="5581145-ALT"
          />

          <div className="our-vision-5581146 pos-abs">
            <span className="our-vision-5581146-0">{`OUR VISION`}</span>
          </div>

          <div className="to-redefine-urb-5581147 pos-abs">
            <span className="to-redefine-urb-5581147-0">{`To redefine urban living by creating timeless villas that embody luxury, comfort, and connection with nature—where every family finds not just a house, but a place to call home.`}</span>
          </div>
        </section>
        {/* Frame 81 */}
        <section className="frame-8-599910 pos-abs">
          <div className="ku-kuberaa-599911 pos-abs">
            <span className="ku-kuberaa-599911-0">{`KU KUBERAA`}</span>
          </div>

          <div className="kuberaa-logo-2-709940 pos-abs">
            <img
              src="https://s3-alpha-sig.figma.com/img/947c/518a/a96e128dd3a5444381161e74e580d564?Expires=1748822400&Key-Pair-Id=APKAQ4GOSFWCW27IBOMQ&Signature=G7pL8R12ijoiHJPKjGj1Ybj18u~OV27YXncaNcp9G5dXnqfKhJPgVC-bCaSSCrX6RAZ62cX8HNKs9h~jod8MD~1hd3WXCo3zYBvujzwFr5zxxQtE6cWJTILbw-mPsQzZcY2brXZ~JEMp6~vJEKIEw98AqnWGdKBkqVZKaZ3kiN9aUwL1io83vEElk84tVBBNAesjTAKI~q1ZeNB52kabLxCVy2pJ7OzJSPm-Q2XWdeAS8AVSdukdUzXgiX1K-scwggFUIPy0vfonyO6~1w5S9XRpw-ga7das0dbnrDAqTwp0b4XUFvbZA6eF97lG6BqySR7y7Wg~q6kf5NJu1~NTOQ__"
              className="pos-abs image-div bg-no-repeat fill-parent bg-cover nodeBg-709940 "
              alt="709940-ALT"
            />{" "}
          </div>

          <div className="licensed-in-ben-599912 pos-abs">
            <span className="licensed-in-ben-599912-0">{`LICENSED IN
BENGALURU`}</span>
          </div>

          <div className="no-99kemparaju--599913 pos-abs">
            <span className="no-99kemparaju--599913-0">{`No. 99,Kemparaju Layout
Hirandahalli Village
Vigro Nagar Post
Bangalore- 560049`}</span>
          </div>

          <div className="krbuildtechs-599917 pos-abs">
            <span className="krbuildtechs-599917-0">{`kr_build_techs`}</span>
          </div>

          <div className="frame-599929 pos-abs">
            <div className="vector-599930 pos-abs">
              <div className="nodeBg-599930 pos-abs pos-init fill-parent image-div bg-contain bg-no-repeat "></div>
            </div>
          </div>

          <div className="frame-599921 pos-abs">
            <div className="vector-599922 pos-abs">
              <div className="nodeBg-599922 pos-abs pos-init fill-parent image-div bg-contain bg-no-repeat "></div>
            </div>
          </div>

          <div className="krbuildtechs-599916 pos-abs">
            <span className="krbuildtechs-599916-0">{`kr_build_techs`}</span>
          </div>

          <div className="krbuildtechs-599918 pos-abs">
            <span className="krbuildtechs-599918-0">{`kr_build_techs`}</span>
          </div>

          <div className="frame-599927 pos-abs">
            <div className="vector-599928 pos-abs">
              <div className="nodeBg-599928 pos-abs pos-init fill-parent image-div bg-contain bg-no-repeat "></div>
            </div>
          </div>

          <div className="krbuildtechsgma-599915 pos-abs">
            <span className="krbuildtechsgma-599915-0">{`<EMAIL>`}</span>
          </div>

          <div className="frame-599923 pos-abs">
            <div className="vector-599924 pos-abs">
              <div className="nodeBg-599924 pos-abs pos-init fill-parent image-div bg-contain bg-no-repeat "></div>
            </div>
          </div>

          <div className="c-91-9845491599-599914 pos-abs">
            <span className="c-91-9845491599-599914-0">{`+91 9845491599`}</span>
          </div>

          <div className="frame-599925 pos-abs">
            <div className="vector-599926 pos-abs">
              <div className="nodeBg-599926 pos-abs pos-init fill-parent image-div bg-contain bg-no-repeat "></div>
            </div>
          </div>

          <div className="krbuildtechs-599919 pos-abs">
            <span className="krbuildtechs-599919-0">{`kr_build_techs`}</span>
          </div>

          <div className="frame-599931 pos-abs">
            <div className="vector-599932 pos-abs">
              <div className="nodeBg-599932 pos-abs pos-init fill-parent image-div bg-contain bg-no-repeat "></div>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
};
ReactDOM.createRoot(document.getElementById("dualite-root")).render(<App />);

// Generated with Dualite Figma Plugin
