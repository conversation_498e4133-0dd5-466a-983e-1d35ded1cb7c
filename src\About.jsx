// About.jsx

import React from "react";
import { Link } from "react-router-dom";
import "./About.css";
import "./home.css";

const About = () => {
  return (
    <div className="modern-layout">
      {/* HEADER/NAVIGATION - copied from home page */}
      <header className="main-header">
        <div className="logo-container">
          <img
            src="https://s3-alpha-sig.figma.com/img/947c/518a/a96e128dd3a5444381161e74e580d564?Expires=1748822400&Key-Pair-Id=APKAQ4GOSFWCW27IBOMQ&Signature=G7pL8R12ijoiHJPKjGj1Ybj18u~OV27YXncaNcp9G5dXnqfKhJPgVC-bCaSSCrX6RAZ62cX8HNKs9h~jod8MD~1hd3WXCo3zYBvujzwFr5zxxQtE6cWJTILbw-mPsQzZcY2brXZ~JEMp6~vJEKIEw98AqnWGdKBkqVZKaZ3kiN9aUwL1io83vEElk84tVBBNAesjTAKI~q1ZeNB52kabLxCVy2pJ7OzJSPm-Q2XWdeAS8AVSdukdUzXgiX1K-scwggFUIPy0vfonyO6~1w5S9XRpw-ga7das0dbnrDAqTwp0b4XUFvbZA6eF97lG6BqySR7y7Wg~q6kf5NJu1~NTOQ__"
            className="logo-image"
            alt="Kuberaa Logo"
          />
        </div>
        <nav className="main-nav">
          <ul className="nav-list">
            <li>
              <Link to="/" className="nav-item">
                Home
              </Link>
            </li>
            <li>
              <a href="#projects" className="nav-item">
                Projects
              </a>
            </li>
            <li>
              <Link to="/about" className="nav-item active">
                About us
              </Link>
            </li>
            <li>
              <a href="#contact" className="nav-item">
                Contact us
              </a>
            </li>
          </ul>
        </nav>
      </header>

      {/* HERO IMAGE SECTION */}
      <section className="about-hero-section">
        <img
          src="https://s3-alpha-sig.figma.com/img/d276/74b9/9f6f1470c95c721f6a45d53df17ee273?Expires=1748822400&Key-Pair-Id=APKAQ4GOSFWCW27IBOMQ&Signature=nhOHRUVuXu7bCK3T2BStBIt1pvMsmVDyJ1idzFYwm-C9PYVQqmoL1cS-iHnHqI6Jtw~TC-pNasl~ne514R0B-OWKuAlARdcYUDGrBSsXCiJ14fEjC-C7zHiQ9j8JVHaHfFvm4e6wrbMOT4vQWRosEXcR2Q9iVX5dosGYBTDYi9e2vPzdTsqn9GO8iuK3JFrjsnZRPvWKUrunX6lGhPt78TfWgaQ3R9sRdrSs1goxlEB3q6qWZWiaJIoOX77rlvw7HyLFBEzBhJYbzEnpyK3Mpm4YrrXbkHAVDtNBzSseBofIQj~FQY9HpVteTq7aXaUu-0Lsl-o0FEVKQa7ayd4DKw__"
          className="about-hero-bg"
          alt="Hero Background"
        />

        {/* KUBERAA WATERMARK - positioned in top left */}
        {/* <div className="about-kuberaa-watermark">KUBERAA</div> */}

        {/* ABOUT US TITLE */}
        <div className="about-us-title">
          <h1>ABOUT US</h1>
        </div>
      </section>

      {/* BEHIND THE SUCCESS SECTION */}
      <section className="about-main-section">
        <div className="container">
          <div className="about-content-wrapper">
            <div className="about-left-content">
              <h2 className="about-section-title">BEHIND THE SUCCESS</h2>

              <div className="about-text-content">
                <p className="about-description-text">
                  Absolutely! Here's a shorter and more concise version of the
                  About Us page for a real estate business:
                  <br />
                  About Us
                  <br />
                  At [Your Company Name], we make real estate simple, personal,
                  and rewarding. Whether you're buying, selling, or investing,
                  our experienced team is here to guide you every step of the
                  way.
                  <br />
                  We specialize in residential and commercial properties,
                  offering expert advice, local market knowledge, and a
                  commitment to client satisfaction.
                  <br />
                  With a passion for property and a focus on results, we're not
                  just helping you find a space—we're helping you find your
                  future.
                </p>
              </div>
            </div>

            {/* CEO SECTION */}
            <div className="about-right-content">
              <div className="ceo-profile">
                <div className="ceo-image-container">
                  <img
                    src="/images/ceo-photo.png"
                    alt="K. Kemparaj"
                    className="ceo-photo"
                    onError={e => {
                      e.target.style.display = "none";
                      e.target.parentElement.innerHTML =
                        '<div style="width: 100%; height: 100%; background: #c59a38; display: flex; align-items: center; justify-content: center; color: white; font-size: 24px; font-weight: bold;">KK</div>';
                    }}
                  />
                </div>
                <div className="ceo-details">
                  <h3 className="ceo-name-title">K. Kemparaj</h3>
                  <p className="ceo-position">CEO OF THE COMPANY</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* OUR MISSION SECTION */}
      <section className="mission-section">
        <div className="mission-container">
          <h2 className="mission-title">OUR MISSION</h2>

          <div className="mission-items">
            <div className="mission-item">
              <p>
                To deliver architecturally distinctive villas that combine
                elegance with thoughtful functionality.
              </p>
            </div>

            <div className="mission-item">
              <p>
                To foster a vibrant, secure, and green community in the heart of
                Bangalore.
              </p>
            </div>

            <div className="mission-item">
              <p>
                To prioritize sustainable practices, premium quality, and
                long-lasting value in every villa we build.
              </p>
            </div>

            <div className="mission-item">
              <p>
                To continuously innovate in design and amenities, keeping
                homeowners at the heart of every decision.
              </p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default About;
