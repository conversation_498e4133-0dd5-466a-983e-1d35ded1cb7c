/* About.css */

@import url("https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,400;0,500;0,600;0,700;0,800;0,900;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");

/* ABOUT PAGE HERO SECTION */
.about-hero-section {
  position: relative;
  width: 100vw;
  height: 60vh;
  margin-top: 80px;
  overflow: hidden;
  display: block;
  left: 0;
  right: 0;
}

/* MAIN LAYOUT */
.modern-layout {
  width: 100%;
  min-height: 100vh;
  background: #ffffff;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  overflow-x: hidden;
}

.about-hero-bg {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* KUBERAA WATERMARK - positioned in top left */
.about-kuberaa-watermark {
  position: absolute;
  top: 30px;
  left: 50px;
  font-family: Montserrat;
  font-weight: 500;
  font-size: 48px;
  color: #ffffff;
  z-index: 10;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  letter-spacing: 2px;
}

/* ABOUT US TITLE */
.about-us-title {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
}

.about-us-title h1 {
  font-family: Montserrat;
  font-weight: 500;
  font-size: 48px;
  color: #ffffff;
  text-align: center;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  letter-spacing: 3px;
}

/* MAIN ABOUT SECTION */
.about-main-section {
  padding: 80px 50px;
  background: #ffffff;
  width: 100%;
  box-sizing: border-box;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0;
  box-sizing: border-box;
}

.about-content-wrapper {
  display: flex;
  gap: 60px;
  align-items: flex-start;
  width: 100%;
  min-height: 500px;
}

.about-left-content {
  flex: 1;
  width: 60%;
  padding-right: 30px;
}

.about-section-title {
  font-family: Montserrat;
  font-weight: 400;
  font-size: 48px;
  letter-spacing: 9.6px;
  color: #c59a38;
  margin-bottom: 40px;
  text-align: left;
}

.about-text-content {
  margin-bottom: 40px;
}

.about-description-text {
  font-family: Montserrat;
  font-weight: 400;
  font-size: 16px;
  line-height: 1.6;
  color: #000000;
  text-align: left;
}

/* CEO SECTION */
.about-right-content {
  flex: 1;
  width: 40%;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding-top: 60px;
  min-height: 400px;
  padding-left: 30px;
}

.ceo-profile {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  width: 100%;
}

.ceo-image-container {
  width: 200px;
  height: 200px;
  border-radius: 50%;
  overflow: hidden;
  margin-bottom: 20px;
  background: #f0f0f0; /* Fallback background */
  border: 3px solid #c59a38; /* Add border to make it visible */
}

.ceo-photo {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.ceo-details {
  text-align: center;
  width: 100%;
}

.ceo-name-title {
  font-family: Montserrat;
  font-weight: 500;
  font-size: 24px;
  color: #c59a38;
  margin-bottom: 8px;
}

.ceo-position {
  font-family: Montserrat;
  font-weight: 400;
  font-size: 14px;
  color: #524b4b;
  letter-spacing: 1px;
}

/* RESPONSIVE DESIGN */
@media (max-width: 768px) {
  .container {
    padding: 0 20px;
  }

  .about-content-wrapper {
    flex-direction: column;
    gap: 40px;
  }

  .about-left-content,
  .about-right-content {
    max-width: 100%;
  }

  .about-section-title {
    font-size: 36px;
    letter-spacing: 6px;
  }

  .about-kuberaa-watermark {
    left: 20px;
    font-size: 36px;
  }

  .about-us-title h1 {
    font-size: 36px;
  }

  .ceo-image-container {
    width: 150px;
    height: 150px;
  }
}
